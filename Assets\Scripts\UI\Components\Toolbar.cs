using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.UI.Config;
using BlastingDesign.Utils;
using BlastingDesign.Events.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 工具栏组件 - 轻量级架构
    /// 负责显示和管理工具栏功能，支持数据驱动配置
    /// </summary>
    [UxmlElement]
    public partial class Toolbar : UIElementBase
    {
        // 资源路径
        protected override string TemplatePath => "UI/Toolbar";

        [Header("Toolbar Settings")]
        private ToolbarConfig config;
        private string configPath = "UI/ToolbarConfig";
        private string selectedTool = "";
        private Dictionary<string, string> exclusiveGroups; // 互斥组管理

        // UI元素引用
        private VisualElement toolGroups;
        private Dictionary<string, Button> toolButtons;
        private Dictionary<string, VisualElement> toolGroupElements;
        private Button currentSelectedButton;

        // 回调系统
        private UICallbackSystem callbackSystem;

        // 重入保护
        private bool isProcessingToolSelection = false;

        public Toolbar()
        {
            elementName = "Toolbar";
            toolButtons = new Dictionary<string, Button>();
            toolGroupElements = new Dictionary<string, VisualElement>();
            exclusiveGroups = new Dictionary<string, string>();
        }

        protected override void CacheUIElements()
        {
            // 缓存主要UI元素
            toolGroups = SafeQuery<VisualElement>("tool-groups");

            // 获取回调系统引用
            callbackSystem = UICallbackSystem.Instance;
        }

        protected override void InitializeData()
        {
            // 加载配置
            LoadConfig();

            // 根据配置生成工具栏
            if (config != null && config.ValidateConfig())
            {
                GenerateToolbarFromConfig();
            }
            else
            {
                Logging.LogWarning("Toolbar", "配置无效，使用默认工具栏");
                CreateDefaultToolbar();
            }
        }

        protected override void SetupEventListeners()
        {
            // 工具栏特定的事件监听器设置
            // 可以在这里添加全局快捷键监听等
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                config = Resources.Load<ToolbarConfig>(configPath);
                if (config == null)
                {
                    Logging.LogWarning("Toolbar", $"未找到配置文件: {configPath}，将创建默认配置");
                    CreateDefaultConfig();
                }
                else
                {
                    Logging.LogInfo("Toolbar", "配置文件加载成功");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("Toolbar", $"加载配置文件失败: {ex.Message}");
                CreateDefaultConfig();
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private void CreateDefaultConfig()
        {
            config = ScriptableObject.CreateInstance<ToolbarConfig>();
            config.CreateDefaultConfig();
        }

        /// <summary>
        /// 根据配置生成工具栏
        /// </summary>
        private void GenerateToolbarFromConfig()
        {
            if (toolGroups == null || config == null) return;

            // 清空现有工具栏
            toolGroups.Clear();
            toolButtons.Clear();
            toolGroupElements.Clear();
            exclusiveGroups.Clear();

            // 按排序顺序生成工具组
            config.SortToolGroups();

            for (int i = 0; i < config.ToolGroups.Count; i++)
            {
                var group = config.ToolGroups[i];
                if (!group.enabled) continue;

                CreateToolGroup(group);

                // 添加分隔符（除了最后一个组）
                if (i < config.ToolGroups.Count - 1 && group.showSeparator)
                {
                    CreateGroupSeparator();
                }
            }

            // 设置默认选中的工具
            SetDefaultSelectedTool();

            Logging.LogInfo("Toolbar", $"根据配置生成了 {config.ToolGroups.Count} 个工具组");
        }

        /// <summary>
        /// 创建工具组
        /// </summary>
        private void CreateToolGroup(ToolbarConfig.ToolGroup group)
        {
            var groupElement = new VisualElement();
            groupElement.name = $"{group.name}-group";
            groupElement.AddToClassList("tool-group");

            if (!string.IsNullOrEmpty(group.customCssClass))
            {
                groupElement.AddToClassList(group.customCssClass);
            }

            // 生成工具按钮
            foreach (var button in group.buttons)
            {
                if (!button.visible) continue;

                CreateToolButton(groupElement, button);
            }

            // 添加到容器
            toolGroups.Add(groupElement);
            toolGroupElements[group.name] = groupElement;
        }

        /// <summary>
        /// 创建工具按钮
        /// </summary>
        private void CreateToolButton(VisualElement parent, ToolbarConfig.ToolButton buttonConfig)
        {
            var button = new Button();
            button.name = buttonConfig.name;
            button.AddToClassList("tool-button");

            // 设置工具类型样式
            button.AddToClassList(buttonConfig.toolType.ToString().ToLower() + "-tool");

            if (!string.IsNullOrEmpty(buttonConfig.customCssClass))
            {
                button.AddToClassList(buttonConfig.customCssClass);
            }

            // 创建图标
            if (!string.IsNullOrEmpty(buttonConfig.iconName))
            {
                var icon = new VisualElement();
                icon.AddToClassList("tool-icon");
                icon.AddToClassList(buttonConfig.iconName);
                button.Add(icon);
            }

            // 创建标签
            if (buttonConfig.showLabel && config.ShowLabels)
            {
                var label = new Label(buttonConfig.displayName);
                label.AddToClassList("tool-label");
                button.Add(label);
            }

            // 设置属性
            button.SetEnabled(buttonConfig.enabled);

            if (!string.IsNullOrEmpty(buttonConfig.tooltip) && config.ShowTooltips)
            {
                button.tooltip = buttonConfig.tooltip;
            }

            // 设置切换按钮样式
            if (buttonConfig.isToggle)
            {
                button.AddToClassList("toggle");
            }

            // 注册点击事件
            button.RegisterCallback<ClickEvent>(evt => OnToolButtonClicked(buttonConfig, button));

            // 添加到父容器和字典
            parent.Add(button);
            toolButtons[buttonConfig.name] = button;

            // 处理互斥组
            if (buttonConfig.isExclusive && !string.IsNullOrEmpty(buttonConfig.exclusiveGroup))
            {
                exclusiveGroups[buttonConfig.name] = buttonConfig.exclusiveGroup;
            }

            // 设置默认选中状态
            if (buttonConfig.defaultSelected)
            {
                selectedTool = buttonConfig.name;
                currentSelectedButton = button;
            }
        }

        /// <summary>
        /// 创建组分隔符
        /// </summary>
        private void CreateGroupSeparator()
        {
            var separator = new VisualElement();
            separator.AddToClassList("tool-group-separator");
            toolGroups.Add(separator);
        }

        /// <summary>
        /// 设置默认选中的工具
        /// </summary>
        private void SetDefaultSelectedTool()
        {
            if (currentSelectedButton != null)
            {
                SetToolButtonSelected(currentSelectedButton, true);
            }
            else
            {
                // 如果没有默认选中的工具，选择第一个可用的工具
                var firstButton = toolButtons.Values.FirstOrDefault(b => b.enabledSelf);
                if (firstButton != null)
                {
                    var buttonName = toolButtons.FirstOrDefault(kvp => kvp.Value == firstButton).Key;
                    selectedTool = buttonName;
                    currentSelectedButton = firstButton;
                    SetToolButtonSelected(firstButton, true);
                }
            }
        }

        /// <summary>
        /// 工具按钮点击处理
        /// </summary>
        private void OnToolButtonClicked(ToolbarConfig.ToolButton buttonConfig, Button button)
        {
            if (buttonConfig.isToggle)
            {
                // 切换按钮逻辑
                HandleToggleButton(buttonConfig, button);
            }
            else
            {
                // 普通工具按钮逻辑
                HandleToolSelection(buttonConfig, button);
            }

            // 执行回调
            if (!string.IsNullOrEmpty(buttonConfig.callbackName) && callbackSystem != null)
            {
                callbackSystem.ExecuteCallback(buttonConfig.callbackName, buttonConfig.parameters);
            }

            // 发布工具按钮点击事件（在重入保护之外，安全发布）
            PublishToolButtonClickedEvents(buttonConfig, button);

            Logging.LogInfo("Toolbar", $"工具按钮点击: {buttonConfig.displayName}");
        }

        /// <summary>
        /// 处理切换按钮
        /// </summary>
        private void HandleToggleButton(ToolbarConfig.ToolButton buttonConfig, Button button)
        {
            // 重入保护 - 防止堆栈溢出
            if (isProcessingToolSelection)
            {
                Logging.LogWarning("Toolbar", $"检测到切换按钮重入调用，忽略: {buttonConfig.name}");
                return;
            }

            try
            {
                isProcessingToolSelection = true;
                bool wasActive = button.ClassListContains("active");
                bool isNowActive = !wasActive;

                if (wasActive)
                {
                    button.RemoveFromClassList("active");
                }
                else
                {
                    button.AddToClassList("active");
                }

                // 发布工具状态变更事件
                // PublishEvent(new ToolStateChangedEvent(
                //     buttonConfig.name,
                //     isNowActive ? ToolState.Active : ToolState.Inactive,
                //     wasActive ? ToolState.Active : ToolState.Inactive,
                //     new { isToggle = true, buttonConfig }
                // ));
            }
            catch (System.Exception ex)
            {
                Logging.LogError("Toolbar", $"处理切换按钮时出错: {ex.Message}");
            }
            finally
            {
                isProcessingToolSelection = false;
            }
        }

        /// <summary>
        /// 处理工具选择
        /// </summary>
        private void HandleToolSelection(ToolbarConfig.ToolButton buttonConfig, Button button)
        {
            // 重入保护 - 防止堆栈溢出
            if (isProcessingToolSelection)
            {
                Logging.LogWarning("Toolbar", $"检测到工具选择重入调用，忽略: {buttonConfig.name}");
                return;
            }

            try
            {
                isProcessingToolSelection = true;
                string previousTool = selectedTool;

                // 处理互斥组
                if (buttonConfig.isExclusive && !string.IsNullOrEmpty(buttonConfig.exclusiveGroup))
                {
                    ClearExclusiveGroup(buttonConfig.exclusiveGroup);
                }

                // 设置当前选中的工具
                if (currentSelectedButton != null)
                {
                    SetToolButtonSelected(currentSelectedButton, false);
                }

                selectedTool = buttonConfig.name;
                currentSelectedButton = button;
                SetToolButtonSelected(button, true);

                // 延迟发布事件，避免在状态变更过程中触发递归调用
                PublishToolSelectionEvents(buttonConfig, previousTool);
            }
            finally
            {
                isProcessingToolSelection = false;
            }
        }

        /// <summary>
        /// 发布工具选择相关事件
        /// </summary>
        private void PublishToolSelectionEvents(ToolbarConfig.ToolButton buttonConfig, string previousTool)
        {
            try
            {
                // 发布工具组切换事件
                if (buttonConfig.isExclusive && !string.IsNullOrEmpty(buttonConfig.exclusiveGroup) &&
                    !string.IsNullOrEmpty(previousTool) && previousTool != buttonConfig.name)
                {
                    // PublishEvent(new ToolGroupSwitchedEvent(
                    //     buttonConfig.exclusiveGroup,
                    //     buttonConfig.name,
                    //     previousTool
                    // ));
                }

                // 发布工具状态变更事件（之前的工具变为非活动状态）
                if (!string.IsNullOrEmpty(previousTool) && previousTool != buttonConfig.name)
                {
                    // PublishEvent(new ToolStateChangedEvent(
                    //     previousTool,
                    //     ToolState.Inactive,
                    //     ToolState.Active
                    // ));
                }

                // 发布工具状态变更事件（当前工具变为活动状态）
                // PublishEvent(new ToolStateChangedEvent(
                //     buttonConfig.name,
                //     ToolState.Active,
                //     ToolState.Inactive
                // ));
            }
            catch (System.Exception ex)
            {
                Logging.LogError("Toolbar", $"发布工具选择事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 发布工具按钮点击事件（安全发布，不会导致重入）
        /// </summary>
        private void PublishToolButtonClickedEvents(ToolbarConfig.ToolButton buttonConfig, Button button)
        {
            try
            {
                // 发布详细的工具按钮点击事件
                PublishEvent(new ToolButtonClickedEvent(
                    buttonConfig.name,
                    buttonConfig.displayName,
                    buttonConfig.isToggle,
                    buttonConfig.isToggle && button.ClassListContains("active"),
                    buttonConfig,
                    buttonConfig.callbackName,
                    buttonConfig.parameters
                ));

                // 发布通用工具选择事件（保持兼容性）
                if (!buttonConfig.isToggle)
                {
                    // PublishEvent(new ToolSelectedEvent(buttonConfig.name));
                }

                // 发布状态消息事件
                // PublishEvent(new StatusMessageEvent($"选择工具: {buttonConfig.displayName}"));

                // 兼容性支持 - 同时触发旧系统事件
                // PublishCompatEvent("ToolSelected", buttonConfig.name);
                // PublishCompatEvent("StatusMessage", $"选择工具: {buttonConfig.displayName}");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("Toolbar", $"发布工具按钮点击事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除互斥组中的其他选中状态
        /// </summary>
        private void ClearExclusiveGroup(string groupName)
        {
            foreach (var kvp in exclusiveGroups)
            {
                if (kvp.Value == groupName && toolButtons.ContainsKey(kvp.Key))
                {
                    SetToolButtonSelected(toolButtons[kvp.Key], false);
                }
            }
        }

        /// <summary>
        /// 设置工具按钮选中状态
        /// </summary>
        private void SetToolButtonSelected(Button button, bool selected)
        {
            if (button == null) return;

            if (selected)
            {
                button.AddToClassList("selected");
                // 添加选中动画
                button.AddToClassList("animate-select");
                // 移除动画类（延迟执行）
                button.schedule.Execute(() => button.RemoveFromClassList("animate-select")).StartingIn(200);
            }
            else
            {
                button.RemoveFromClassList("selected");
            }
        }

        /// <summary>
        /// 创建默认工具栏（备用方案）
        /// </summary>
        private void CreateDefaultToolbar()
        {
            // 简单的默认工具栏实现
            var selectButton = new Button();
            selectButton.AddToClassList("tool-button");
            selectButton.Add(new VisualElement { name = "select-icon" });
            selectButton.Add(new Label("选择"));
            toolGroups.Add(selectButton);

            Logging.LogInfo("Toolbar", "创建了默认工具栏");
        }

        /// <summary>
        /// 获取当前选中的工具
        /// </summary>
        public string GetSelectedTool()
        {
            return selectedTool;
        }

        /// <summary>
        /// 设置选中的工具
        /// </summary>
        public void SetSelectedTool(string toolName)
        {
            if (toolButtons.ContainsKey(toolName))
            {
                var button = toolButtons[toolName];
                var buttonConfig = GetToolButtonConfig(toolName);
                if (buttonConfig != null)
                {
                    HandleToolSelection(buttonConfig, button);
                }
            }
        }

        /// <summary>
        /// 获取工具按钮配置
        /// </summary>
        private ToolbarConfig.ToolButton GetToolButtonConfig(string toolName)
        {
            if (config == null) return null;

            foreach (var group in config.ToolGroups)
            {
                var button = group.buttons.Find(b => b.name == toolName);
                if (button != null) return button;
            }
            return null;
        }

        protected override void RemoveEventListeners()
        {
            // 清理事件监听器
            foreach (var button in toolButtons.Values)
            {
                button?.UnregisterCallback<ClickEvent>(evt => { });
            }
        }

        public override void Cleanup()
        {
            base.Cleanup();

            toolButtons?.Clear();
            toolGroupElements?.Clear();
            exclusiveGroups?.Clear();
            currentSelectedButton = null;
            config = null;

            Logging.LogInfo("Toolbar", "工具栏清理完成");
        }
    }
}
