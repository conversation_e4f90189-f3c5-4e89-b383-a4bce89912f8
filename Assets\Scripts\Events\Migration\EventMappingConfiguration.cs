using System;
using System.Collections.Generic;
using UnityEngine;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Events.Migration
{
    /// <summary>
    /// 事件映射配置管理器
    /// 负责管理新旧事件系统之间的映射关系
    /// </summary>
    [CreateAssetMenu(fileName = "EventMappingConfiguration", menuName = "BlastingDesign/Events/Event Mapping Configuration")]
    public class EventMappingConfiguration : ScriptableObject
    {
        [Header("映射配置")]
        [SerializeField] private List<UnityEventMapping> unityEventMappings = new List<UnityEventMapping>();
        [SerializeField] private List<LegacyEventMapping> legacyEventMappings = new List<LegacyEventMapping>();
        [SerializeField] private List<ComponentMappingRule> componentMappingRules = new List<ComponentMappingRule>();

        [Header("迁移设置")]
        [SerializeField] private bool enableAutoMigration = true;
        [SerializeField] private bool enableValidation = true;
        [SerializeField] private MigrationPriority defaultPriority = MigrationPriority.Medium;

        /// <summary>
        /// 初始化默认映射配置
        /// </summary>
        [ContextMenu("Initialize Default Mappings")]
        public void InitializeDefaultMappings()
        {
            unityEventMappings.Clear();
            legacyEventMappings.Clear();
            componentMappingRules.Clear();

            // 初始化工具栏事件映射
            InitializeToolbarMappings();

            // 初始化面板事件映射
            InitializePanelMappings();

            // 初始化选择事件映射
            InitializeSelectionMappings();

            // 初始化状态栏事件映射
            InitializeStatusBarMappings();

            // 初始化属性事件映射
            InitializePropertyMappings();

            // 初始化组件映射规则
            InitializeComponentMappingRules();

            Debug.Log("EventMappingConfiguration: 默认映射配置已初始化");
        }

        /// <summary>
        /// 获取UnityEvent映射
        /// </summary>
        public UnityEventMapping GetUnityEventMapping(string eventName)
        {
            return unityEventMappings.Find(m => m.UnityEventName == eventName);
        }

        /// <summary>
        /// 获取旧事件映射
        /// </summary>
        public LegacyEventMapping GetLegacyEventMapping(string eventName)
        {
            return legacyEventMappings.Find(m => m.LegacyEventName == eventName);
        }

        /// <summary>
        /// 获取组件映射规则
        /// </summary>
        public ComponentMappingRule GetComponentMappingRule(string componentName)
        {
            return componentMappingRules.Find(r => r.ComponentName == componentName);
        }

        /// <summary>
        /// 获取所有映射
        /// </summary>
        public EventTypeMappingConfig GetMappingConfig()
        {
            var config = new EventTypeMappingConfig();

            // 添加UnityEvent映射
            foreach (var mapping in unityEventMappings)
            {
                config.AddUnityEventMapping(mapping.UnityEventName, mapping.NewEventType);

                if (!string.IsNullOrEmpty(mapping.ConversionCode))
                {
                    var rule = new MigrationRule
                    {
                        EventName = mapping.UnityEventName,
                        NewEventType = mapping.NewEventType,
                        ConversionCode = mapping.ConversionCode,
                        Priority = mapping.Priority,
                        RequiresManualIntervention = mapping.RequiresManualIntervention,
                        Notes = mapping.Notes
                    };

                    config.AddMigrationRule(mapping.UnityEventName, rule);
                }
            }

            // 添加旧事件映射
            foreach (var mapping in legacyEventMappings)
            {
                config.AddLegacyEventMapping(mapping.LegacyEventName, mapping.NewEventType);

                if (!string.IsNullOrEmpty(mapping.ConversionCode))
                {
                    var rule = new MigrationRule
                    {
                        EventName = mapping.LegacyEventName,
                        NewEventType = mapping.NewEventType,
                        ConversionCode = mapping.ConversionCode,
                        Priority = mapping.Priority,
                        RequiresManualIntervention = mapping.RequiresManualIntervention,
                        Notes = mapping.Notes
                    };

                    config.AddMigrationRule(mapping.LegacyEventName, rule);
                }
            }

            return config;
        }

        #region 初始化方法

        private void InitializeToolbarMappings()
        {
            // 工具栏UnityEvent映射
            unityEventMappings.AddRange(new[]
            {
                new UnityEventMapping
                {
                    UnityEventName = "OnMenuItemClicked",
                    NewEventType = "MenuItemClickedEvent",
                    ConversionCode = "_eventBus.Publish(new MenuItemClickedEvent(menuItem));",
                    Priority = MigrationPriority.High,
                    Notes = "菜单项点击事件，高优先级迁移"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnToolSelected",
                    NewEventType = "ToolSelectedEvent",
                    ConversionCode = "_eventBus.Publish(new ToolSelectedEvent(toolName));",
                    Priority = MigrationPriority.High,
                    Notes = "工具选择事件，高优先级迁移"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnToolbarVisibilityChanged",
                    NewEventType = "ToolbarVisibilityChangedEvent",
                    ConversionCode = "_eventBus.Publish(new ToolbarVisibilityChangedEvent(isVisible));",
                    Priority = MigrationPriority.Medium,
                    Notes = "工具栏可见性变更事件"
                }
            });
        }

        private void InitializePanelMappings()
        {
            // 面板UnityEvent映射
            unityEventMappings.AddRange(new[]
            {
                new UnityEventMapping
                {
                    UnityEventName = "OnPanelCollapsed",
                    NewEventType = "PanelCollapsedEvent",
                    ConversionCode = "_eventBus.Publish(new PanelCollapsedEvent(panelName, isCollapsed));",
                    Priority = MigrationPriority.Medium,
                    Notes = "面板折叠事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnPanelResized",
                    NewEventType = "PanelResizedEvent",
                    ConversionCode = "_eventBus.Publish(new PanelResizedEvent(panelName, newSize));",
                    Priority = MigrationPriority.Medium,
                    Notes = "面板调整大小事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnPanelVisibilityChanged",
                    NewEventType = "PanelVisibilityChangedEvent",
                    ConversionCode = "_eventBus.Publish(new PanelVisibilityChangedEvent(panelName, isVisible));",
                    Priority = MigrationPriority.Medium,
                    Notes = "面板可见性变更事件"
                }
            });
        }

        private void InitializeSelectionMappings()
        {
            // 选择UnityEvent映射
            unityEventMappings.AddRange(new[]
            {
                new UnityEventMapping
                {
                    UnityEventName = "OnObjectSelected",
                    NewEventType = "ObjectSelectedEvent",
                    ConversionCode = "_eventBus.Publish(new ObjectSelectedEvent(selectedObject));",
                    Priority = MigrationPriority.High,
                    Notes = "对象选择事件，高优先级迁移"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnMultipleObjectsSelected",
                    NewEventType = "MultipleObjectsSelectedEvent",
                    ConversionCode = "_eventBus.Publish(new MultipleObjectsSelectedEvent(selectedObjects));",
                    Priority = MigrationPriority.High,
                    Notes = "多对象选择事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnSelectionCleared",
                    NewEventType = "SelectionClearedEvent",
                    ConversionCode = "_eventBus.Publish(new SelectionClearedEvent());",
                    Priority = MigrationPriority.High,
                    Notes = "选择清除事件"
                }
            });
        }

        private void InitializeStatusBarMappings()
        {
            // 状态栏UnityEvent映射
            unityEventMappings.AddRange(new[]
            {
                new UnityEventMapping
                {
                    UnityEventName = "OnStatusMessageChanged",
                    NewEventType = "StatusMessageEvent",
                    ConversionCode = "_eventBus.Publish(new StatusMessageEvent(message, StatusMessageType.Info));",
                    Priority = MigrationPriority.Medium,
                    Notes = "状态消息变更事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnMouseWorldPositionChanged",
                    NewEventType = "MouseWorldPositionChangedEvent",
                    ConversionCode = "_eventBus.Publish(new MouseWorldPositionChangedEvent(worldPosition));",
                    Priority = MigrationPriority.Low,
                    Notes = "鼠标世界坐标变更事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnMouseGeoPositionChanged",
                    NewEventType = "MouseGeoPositionChangedEvent",
                    ConversionCode = "_eventBus.Publish(new MouseGeoPositionChangedEvent(geoPosition));",
                    Priority = MigrationPriority.Low,
                    Notes = "鼠标地理坐标变更事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnToolTipChanged",
                    NewEventType = "ToolTipChangedEvent",
                    ConversionCode = "_eventBus.Publish(new ToolTipChangedEvent(toolTip));",
                    Priority = MigrationPriority.Low,
                    Notes = "工具提示变更事件"
                }
            });
        }

        private void InitializePropertyMappings()
        {
            // 属性UnityEvent映射
            unityEventMappings.AddRange(new[]
            {
                new UnityEventMapping
                {
                    UnityEventName = "OnPropertyChanged",
                    NewEventType = "PropertyChangedEvent",
                    ConversionCode = "_eventBus.Publish(new PropertyChangedEvent(target, propertyName, oldValue, newValue));",
                    Priority = MigrationPriority.Medium,
                    Notes = "属性变更事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnPropertyEditorRequested",
                    NewEventType = "PropertyEditorRequestedEvent",
                    ConversionCode = "_eventBus.Publish(new PropertyEditorRequestedEvent(propertyType));",
                    Priority = MigrationPriority.Medium,
                    Notes = "属性编辑器请求事件"
                },
                new UnityEventMapping
                {
                    UnityEventName = "OnPropertiesRefreshRequested",
                    NewEventType = "PropertiesRefreshRequestedEvent",
                    ConversionCode = "_eventBus.Publish(new PropertiesRefreshRequestedEvent());",
                    Priority = MigrationPriority.Medium,
                    Notes = "属性刷新请求事件"
                }
            });

            // 自定义事件映射
            unityEventMappings.Add(new UnityEventMapping
            {
                UnityEventName = "OnCustomEvent",
                NewEventType = "CustomEvent",
                ConversionCode = "_eventBus.Publish(new CustomEvent(eventName, data));",
                Priority = MigrationPriority.Low,
                Notes = "自定义事件"
            });
        }

        private void InitializeComponentMappingRules()
        {
            // 组件映射规则
            componentMappingRules.AddRange(new[]
            {
                new ComponentMappingRule
                {
                    ComponentName = "Toolbar",
                    Priority = MigrationPriority.High,
                    RequiresManualIntervention = false,
                    EstimatedMigrationTime = 240, // 4小时
                    Notes = "工具栏组件，包含菜单和工具选择事件"
                },
                new ComponentMappingRule
                {
                    ComponentName = "StatusBar",
                    Priority = MigrationPriority.High,
                    RequiresManualIntervention = false,
                    EstimatedMigrationTime = 120, // 2小时
                    Notes = "状态栏组件，包含状态消息和坐标显示事件"
                },
                new ComponentMappingRule
                {
                    ComponentName = "MenuBar",
                    Priority = MigrationPriority.High,
                    RequiresManualIntervention = true,
                    EstimatedMigrationTime = 300, // 5小时
                    Notes = "菜单栏组件，复杂的下拉菜单逻辑需要手动处理"
                },
                new ComponentMappingRule
                {
                    ComponentName = "LeftPanel",
                    Priority = MigrationPriority.High,
                    RequiresManualIntervention = true,
                    EstimatedMigrationTime = 300, // 5小时
                    Notes = "左侧面板组件，包含树形控件和面板状态事件"
                }
            });
        }

        #endregion
    }
}
